import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { Report, ReportStatus } from "@shared/api";

// Generate historical data for the past 7 days
const generateHistoricalData = (): Report[] => {
  const reports: Report[] = [];
  const reportNames = [
    "Sales Report",
    "Customer List",
    "Inventory Summary",
    "Financial Overview",
    "Marketing Analytics",
    "Weekly Summary",
    "User Activity Report",
    "Performance Metrics",
    "Compliance Report",
    "Quality Assurance Report",
  ];

  const statuses: ReportStatus[] = ["Generated", "Pending", "Failed"];

  // Generate data for past 7 days
  for (let dayOffset = 0; dayOffset < 7; dayOffset++) {
    const date = new Date();
    date.setDate(date.getDate() - dayOffset);

    // Each day has multiple report runs at different times
    const reportsPerDay = Math.floor(Math.random() * 5) + 3; // 3-7 reports per day

    for (let i = 0; i < reportsPerDay; i++) {
      const reportName =
        reportNames[Math.floor(Math.random() * reportNames.length)];
      const status = statuses[Math.floor(Math.random() * statuses.length)];

      // Random time during the day
      const hours = Math.floor(Math.random() * 24);
      const minutes = Math.floor(Math.random() * 60);
      const reportDate = new Date(date);
      reportDate.setHours(hours, minutes, 0, 0);

      reports.push({
        id: `${reportDate.toISOString()}-${reportName.replace(/\s+/g, "-").toLowerCase()}`,
        name: reportName,
        status,
        updatedAt: reportDate.toISOString(),
      });
    }
  }

  // Sort by date (newest first)
  return reports.sort(
    (a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime(),
  );
};

export const handleHistoricalReports: RequestHandler = (req, res) => {
  const { search, status, days } = req.query;

  let reports = generateHistoricalData();

  // Filter by search term
  if (search && typeof search === "string") {
    const searchLower = search.toLowerCase();
    reports = reports.filter((report) =>
      report.name.toLowerCase().includes(searchLower),
    );
  }

  // Filter by status
  if (status && typeof status === "string" && status !== "All") {
    reports = reports.filter((report) => report.status === status);
  }

  // Filter by days
  if (days && typeof days === "string") {
    const daysNum = parseInt(days);
    if (!isNaN(daysNum)) {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysNum);
      reports = reports.filter(
        (report) => new Date(report.updatedAt) >= cutoffDate,
      );
    }
  }

  res.json({
    reports,
    totalCount: reports.length,
    lastFetchTime: new Date().toISOString(),
  });
};
