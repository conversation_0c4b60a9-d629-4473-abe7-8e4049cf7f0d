import { Report } from "@shared/api";
import { StatusBadge } from "./StatusBadge";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { formatDistanceToNow } from "date-fns";
import { Clock, FileText } from "lucide-react";
import { cn } from "@/lib/utils";

interface ReportCardProps {
  report: Report;
  isNew?: boolean;
}

export function ReportCard({ report, isNew }: ReportCardProps) {
  const timeAgo = formatDistanceToNow(new Date(report.updatedAt), {
    addSuffix: true,
  });

  return (
    <Card
      className={cn(
        "transition-all duration-300 hover:shadow-md hover:-translate-y-1",
        "border-l-4",
        report.status === "Generated" && "border-l-emerald-500",
        report.status === "Pending" && "border-l-amber-500",
        report.status === "Failed" && "border-l-red-500",
        isNew && "animate-in slide-in-from-bottom-5 duration-500",
      )}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-3">
          <div className="flex items-center gap-3 min-w-0 flex-1">
            <div
              className={cn(
                "flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center",
                report.status === "Generated" &&
                  "bg-emerald-100 text-emerald-600 dark:bg-emerald-900/30 dark:text-emerald-400",
                report.status === "Pending" &&
                  "bg-amber-100 text-amber-600 dark:bg-amber-900/30 dark:text-amber-400",
                report.status === "Failed" &&
                  "bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400",
              )}
            >
              <FileText size={20} />
            </div>
            <div className="min-w-0 flex-1">
              <h3 className="font-semibold text-base truncate">
                {report.name}
              </h3>
              <div className="flex items-center gap-1 text-sm text-muted-foreground mt-1">
                <Clock size={14} />
                <span>Updated {timeAgo}</span>
              </div>
            </div>
          </div>
          <StatusBadge status={report.status} />
        </div>
      </CardHeader>
    </Card>
  );
}
