import { ReportStatus } from "@shared/api";
import { cn } from "@/lib/utils";

interface StatusBadgeProps {
  status: ReportStatus;
  className?: string;
}

export function StatusBadge({ status, className }: StatusBadgeProps) {
  const statusConfig = {
    Generated: {
      bgColor: "bg-emerald-100 dark:bg-emerald-900/30",
      textColor: "text-emerald-800 dark:text-emerald-200",
      borderColor: "border-emerald-200 dark:border-emerald-700",
      dotColor: "bg-emerald-500",
    },
    Pending: {
      bgColor: "bg-amber-100 dark:bg-amber-900/30",
      textColor: "text-amber-800 dark:text-amber-200",
      borderColor: "border-amber-200 dark:border-amber-700",
      dotColor: "bg-amber-500",
    },
    Failed: {
      bgColor: "bg-red-100 dark:bg-red-900/30",
      textColor: "text-red-800 dark:text-red-200",
      borderColor: "border-red-200 dark:border-red-700",
      dotColor: "bg-red-500",
    },
  };

  const config = statusConfig[status];

  return (
    <div
      className={cn(
        "inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium border transition-all duration-200",
        config.bgColor,
        config.textColor,
        config.borderColor,
        className,
      )}
    >
      <div
        className={cn(
          "w-2 h-2 rounded-full",
          config.dotColor,
          status === "Pending" && "animate-pulse",
        )}
      />
      {status}
    </div>
  );
}
