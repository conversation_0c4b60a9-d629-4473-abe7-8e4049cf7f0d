import { useEffect, useState, useCallback } from "react";
import { ReportsResponse, Report } from "@shared/api";
import { ReportCard } from "@/components/dashboard/ReportCard";
import {
  FilterControls,
  FilterType,
} from "@/components/dashboard/FilterControls";
import { HistoricalReports } from "@/components/dashboard/HistoricalReports";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { RefreshCw, Activity, History, BarChart3 } from "lucide-react";
import { formatDistanceToNow } from "date-fns";

export default function Index() {
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [lastFetchTime, setLastFetchTime] = useState<string>("");
  const [filter, setFilter] = useState<FilterType>("All");
  const [newReportIds, setNewReportIds] = useState<Set<string>>(new Set());

  const fetchReports = useCallback(
    async (isManualRefresh = false) => {
      try {
        if (isManualRefresh) {
          setRefreshing(true);
        }

        const response = await fetch("/api/reports");
        const data: ReportsResponse = await response.json();

        // Track new reports for animation
        const existingIds = new Set(reports.map((r) => r.id));
        const newIds = new Set(
          data.reports.filter((r) => !existingIds.has(r.id)).map((r) => r.id),
        );
        setNewReportIds(newIds);

        setReports(data.reports);
        setLastFetchTime(data.lastFetchTime);
        setLoading(false);

        // Clear new report indicators after animation
        setTimeout(() => setNewReportIds(new Set()), 1000);
      } catch (error) {
        console.error("Error fetching reports:", error);
        setLoading(false);
      } finally {
        if (isManualRefresh) {
          setRefreshing(false);
        }
      }
    },
    [reports],
  );

  const handleManualRefresh = () => {
    fetchReports(true);
  };

  // Initial fetch and polling setup
  useEffect(() => {
    fetchReports();

    // Set up polling every 10 seconds
    const interval = setInterval(() => {
      fetchReports();
    }, 10000);

    return () => clearInterval(interval);
  }, []);

  // Filter reports based on selected filter
  const filteredReports = reports.filter((report) => {
    if (filter === "All") return true;
    return report.status === filter;
  });

  // Get status counts for display
  const statusCounts = {
    total: reports.length,
    generated: reports.filter((r) => r.status === "Generated").length,
    pending: reports.filter((r) => r.status === "Pending").length,
    failed: reports.filter((r) => r.status === "Failed").length,
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin h-12 w-12 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-2">
            <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
              <Activity className="w-6 h-6 text-primary-foreground" />
            </div>
            <h1 className="text-3xl font-bold text-foreground">
              Report Status Dashboard
            </h1>
          </div>
          <p className="text-muted-foreground text-lg">
            Monitor the status of all your reports in real-time
          </p>
        </div>

        {/* Tabs */}
        <Tabs defaultValue="live" className="space-y-6">
          <TabsList className="grid w-full max-w-md grid-cols-2">
            <TabsTrigger value="live" className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4" />
              Live Status
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center gap-2">
              <History className="w-4 h-4" />
              Historical Data
            </TabsTrigger>
          </TabsList>

          <TabsContent value="live" className="space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-card rounded-lg border p-6">
                <div className="text-2xl font-bold text-foreground">
                  {statusCounts.total}
                </div>
                <p className="text-sm text-muted-foreground">Total Reports</p>
              </div>
              <div className="bg-card rounded-lg border p-6">
                <div className="text-2xl font-bold text-emerald-600">
                  {statusCounts.generated}
                </div>
                <p className="text-sm text-muted-foreground">Generated</p>
              </div>
              <div className="bg-card rounded-lg border p-6">
                <div className="text-2xl font-bold text-amber-600">
                  {statusCounts.pending}
                </div>
                <p className="text-sm text-muted-foreground">Pending</p>
              </div>
              <div className="bg-card rounded-lg border p-6">
                <div className="text-2xl font-bold text-red-600">
                  {statusCounts.failed}
                </div>
                <p className="text-sm text-muted-foreground">Failed</p>
              </div>
            </div>

            {/* Controls */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <FilterControls
                currentFilter={filter}
                onFilterChange={setFilter}
              />

              <div className="flex items-center gap-4">
                {lastFetchTime && (
                  <span className="text-sm text-muted-foreground">
                    Last updated{" "}
                    {formatDistanceToNow(new Date(lastFetchTime), {
                      addSuffix: true,
                    })}
                  </span>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleManualRefresh}
                  disabled={refreshing}
                >
                  <RefreshCw
                    className={`w-4 h-4 mr-2 ${refreshing ? "animate-spin" : ""}`}
                  />
                  Refresh
                </Button>
              </div>
            </div>

            {/* Reports Grid */}
            {filteredReports.length === 0 ? (
              <div className="bg-card rounded-lg border p-12 text-center">
                <Activity className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-foreground mb-2">
                  {filter === "All"
                    ? "No reports found"
                    : `No ${filter.toLowerCase()} reports`}
                </h3>
                <p className="text-muted-foreground">
                  {filter === "All"
                    ? "Reports will appear here once they're added to the system."
                    : `There are currently no reports with ${filter.toLowerCase()} status.`}
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredReports.map((report) => (
                  <ReportCard
                    key={report.id}
                    report={report}
                    isNew={newReportIds.has(report.id)}
                  />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="history">
            <HistoricalReports />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
