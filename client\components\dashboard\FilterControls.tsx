import { ReportStatus } from "@shared/api";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export type FilterType = "All" | ReportStatus;

interface FilterControlsProps {
  currentFilter: FilterType;
  onFilterChange: (filter: FilterType) => void;
}

export function FilterControls({
  currentFilter,
  onFilterChange,
}: FilterControlsProps) {
  return (
    <div className="flex items-center gap-2">
      <span className="text-sm font-medium text-muted-foreground">Filter:</span>
      <Select
        value={currentFilter}
        onValueChange={(value) => onFilterChange(value as FilterType)}
      >
        <SelectTrigger className="w-[140px]">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="All">All Reports</SelectItem>
          <SelectItem value="Generated">Generated</SelectItem>
          <SelectItem value="Pending">Pending</SelectItem>
          <SelectItem value="Failed">Failed</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
}
