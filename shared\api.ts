/**
 * Shared code between client and server
 * Useful to share types between client and server
 * and/or small pure JS functions that can be used on both client and server
 */

/**
 * Example response type for /api/demo
 */
export interface DemoResponse {
  message: string;
}

/**
 * Report status types
 */
export type ReportStatus = "Generated" | "Pending" | "Failed";

/**
 * Report interface
 */
export interface Report {
  id: string;
  name: string;
  status: ReportStatus;
  updatedAt: string;
}

/**
 * Reports API response
 */
export interface ReportsResponse {
  reports: Report[];
  lastFetchTime: string;
}
