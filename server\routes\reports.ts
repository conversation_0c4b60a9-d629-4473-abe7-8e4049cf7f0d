import { Request<PERSON><PERSON><PERSON> } from "express";
import { ReportsResponse, Report, ReportStatus } from "@shared/api";

// Mock data - in a real app this would come from a database
const mockReports: Report[] = [
  {
    id: "1",
    name: "Sales Report",
    status: "Generated",
    updatedAt: new Date(Date.now() - 300000).toISOString(), // 5 minutes ago
  },
  {
    id: "2",
    name: "Customer List",
    status: "Pending",
    updatedAt: new Date(Date.now() - 120000).toISOString(), // 2 minutes ago
  },
  {
    id: "3",
    name: "Inventory Summary",
    status: "Failed",
    updatedAt: new Date(Date.now() - 600000).toISOString(), // 10 minutes ago
  },
  {
    id: "4",
    name: "Financial Overview",
    status: "Generated",
    updatedAt: new Date(Date.now() - 900000).toISOString(), // 15 minutes ago
  },
  {
    id: "5",
    name: "Marketing Analytics",
    status: "Pending",
    updatedAt: new Date(Date.now() - 180000).toISOString(), // 3 minutes ago
  },
];

// Simulate dynamic status changes
const getRandomStatus = (): ReportStatus => {
  const statuses: ReportStatus[] = ["Generated", "Pending", "Failed"];
  return statuses[Math.floor(Math.random() * statuses.length)];
};

export const handleReports: RequestHandler = (req, res) => {
  // Simulate some reports changing status randomly (10% chance each)
  const updatedReports = mockReports.map((report) => {
    if (Math.random() < 0.1) {
      return {
        ...report,
        status: getRandomStatus(),
        updatedAt: new Date().toISOString(),
      };
    }
    return report;
  });

  // Occasionally add a new report (5% chance)
  if (Math.random() < 0.05 && updatedReports.length < 8) {
    const newReportNames = [
      "Weekly Summary",
      "User Activity Report",
      "Performance Metrics",
      "Compliance Report",
      "Quality Assurance Report",
    ];

    const unusedNames = newReportNames.filter(
      (name) => !updatedReports.some((report) => report.name === name),
    );

    if (unusedNames.length > 0) {
      const randomName =
        unusedNames[Math.floor(Math.random() * unusedNames.length)];
      updatedReports.push({
        id: String(Date.now()),
        name: randomName,
        status: getRandomStatus(),
        updatedAt: new Date().toISOString(),
      });
    }
  }

  const response: ReportsResponse = {
    reports: updatedReports,
    lastFetchTime: new Date().toISOString(),
  };

  res.json(response);
};
